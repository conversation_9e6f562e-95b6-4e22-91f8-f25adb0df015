USE [LibraryManagement]
GO

/****** Object:  Table [dbo].[__EFMigrationsHistory]    Script Date: 9/2/2025 9:30:00 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[__EFMigrationsHistory](
	[MigrationId] [nvarchar](150) NOT NULL,
	[ProductVersion] [nvarchar](32) NOT NULL,
 CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY CLUSTERED 
(
	[MigrationId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Authors]    Script Date: 9/2/2025 9:30:00 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Authors](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[FullName] [nvarchar](255) NOT NULL,
	[Email] [nvarchar](255) NOT NULL,
	[Biography] [nvarchar](500) NULL,
	[Nationality] [nvarchar](100) NULL,
	[BirthDate] [datetime2](7) NOT NULL,
	[CreateAt] [datetime2](7) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Genres]    Script Date: 9/2/2025 9:30:00 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Genres](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Description] [nvarchar](500) NULL,
	[CreateAt] [datetime2](7) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Books]    Script Date: 9/2/2025 9:30:00 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Books](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Title] [nvarchar](255) NOT NULL,
	[ISBN] [nvarchar](13) NULL,
	[Description] [nvarchar](1000) NULL,
	[Price] [decimal](18, 2) NOT NULL,
	[Stock] [int] NOT NULL,
	[PublishedDate] [datetime2](7) NOT NULL,
	[Publisher] [nvarchar](100) NULL,
	[Pages] [int] NOT NULL,
	[Language] [nvarchar](50) NULL,
	[CreateAt] [datetime2](7) NOT NULL,
	[AuthorId] [int] NOT NULL,
	[GenreId] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

-- Insert sample data for Authors
SET IDENTITY_INSERT [dbo].[Authors] ON 

INSERT [dbo].[Authors] ([Id], [FullName], [Email], [Biography], [Nationality], [BirthDate], [CreateAt]) VALUES 
(1, N'Nguyễn Nhật Ánh', N'<EMAIL>', N'Nhà văn nổi tiếng với các tác phẩm văn học thiếu nhi', N'Việt Nam', CAST(N'1955-05-07T00:00:00.0000000' AS DateTime2), CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(2, N'Tô Hoài', N'<EMAIL>', N'Tác giả của tác phẩm Dế Mèn phiêu lưu ký', N'Việt Nam', CAST(N'1920-09-27T00:00:00.0000000' AS DateTime2), CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(3, N'Nam Cao', N'<EMAIL>', N'Nhà văn hiện thực phê phán nổi tiếng', N'Việt Nam', CAST(N'1915-10-29T00:00:00.0000000' AS DateTime2), CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(4, N'Haruki Murakami', N'<EMAIL>', N'Tiểu thuyết gia Nhật Bản nổi tiếng thế giới', N'Nhật Bản', CAST(N'1949-01-12T00:00:00.0000000' AS DateTime2), CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(5, N'J.K. Rowling', N'<EMAIL>', N'Tác giả của series Harry Potter', N'Anh', CAST(N'1965-07-31T00:00:00.0000000' AS DateTime2), CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2))

SET IDENTITY_INSERT [dbo].[Authors] OFF
GO

-- Insert sample data for Genres
SET IDENTITY_INSERT [dbo].[Genres] ON 

INSERT [dbo].[Genres] ([Id], [Name], [Description], [CreateAt]) VALUES 
(1, N'Văn học thiếu nhi', N'Sách dành cho trẻ em và thanh thiếu niên', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(2, N'Tiểu thuyết', N'Tác phẩm văn học dài, có cốt truyện phức tạp', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(3, N'Truyện ngắn', N'Tác phẩm văn học ngắn, tập trung vào một sự kiện', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(4, N'Khoa học viễn tưởng', N'Thể loại văn học về tương lai và công nghệ', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(5, N'Phiêu lưu', N'Thể loại kể về những cuộc phiêu lưu, mạo hiểm', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2)),
(6, N'Kỳ ảo', N'Thể loại có yếu tố ma thuật, siêu nhiên', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2))

SET IDENTITY_INSERT [dbo].[Genres] OFF
GO

-- Insert sample data for Books
SET IDENTITY_INSERT [dbo].[Books] ON

INSERT [dbo].[Books] ([Id], [Title], [ISBN], [Description], [Price], [Stock], [PublishedDate], [Publisher], [Pages], [Language], [CreateAt], [AuthorId], [GenreId]) VALUES
(1, N'Tôi Thấy Hoa Vàng Trên Cỏ Xanh', N'9786041001234', N'Câu chuyện tuổi thơ đầy cảm động về tình anh em', CAST(89000.00 AS Decimal(18, 2)), 50, CAST(N'2010-12-01T00:00:00.0000000' AS DateTime2), N'NXB Trẻ', 368, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 1, 1),
(2, N'Cho Tôi Xin Một Vé Đi Tuổi Thơ', N'9786041001235', N'Hồi ký tuổi thơ đầy hoài niệm', CAST(75000.00 AS Decimal(18, 2)), 30, CAST(N'2018-05-15T00:00:00.0000000' AS DateTime2), N'NXB Kim Đồng', 280, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 1, 1),
(3, N'Dế Mèn Phiêu Lưu Ký', N'9786041001236', N'Cuộc phiêu lưu của chú dế mèn trong thế giới côn trùng', CAST(65000.00 AS Decimal(18, 2)), 40, CAST(N'1941-01-01T00:00:00.0000000' AS DateTime2), N'NXB Kim Đồng', 200, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 2, 5),
(4, N'Chí Phèo', N'9786041001237', N'Tác phẩm kinh điển về số phận con người', CAST(45000.00 AS Decimal(18, 2)), 25, CAST(N'1941-01-01T00:00:00.0000000' AS DateTime2), N'NXB Văn Học', 120, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 3, 3),
(5, N'Lão Hạc', N'9786041001238', N'Câu chuyện cảm động về tình người', CAST(42000.00 AS Decimal(18, 2)), 35, CAST(N'1943-01-01T00:00:00.0000000' AS DateTime2), N'NXB Văn Học', 95, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 3, 3),
(6, N'Norwegian Wood', N'9786041001239', N'Tiểu thuyết lãng mạn nổi tiếng của Murakami', CAST(150000.00 AS Decimal(18, 2)), 20, CAST(N'1987-08-01T00:00:00.0000000' AS DateTime2), N'NXB Hội Nhà Văn', 320, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 4, 2),
(7, N'Kafka on the Shore', N'9786041001240', N'Tác phẩm kỳ ảo đầy triết lý', CAST(180000.00 AS Decimal(18, 2)), 15, CAST(N'2002-09-12T00:00:00.0000000' AS DateTime2), N'NXB Hội Nhà Văn', 480, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 4, 2),
(8, N'Harry Potter và Hòn Đá Phù Thủy', N'9786041001241', N'Cuộc phiêu lưu đầu tiên của Harry Potter', CAST(120000.00 AS Decimal(18, 2)), 60, CAST(N'1997-06-26T00:00:00.0000000' AS DateTime2), N'NXB Trẻ', 320, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 5, 6),
(9, N'Harry Potter và Phòng Chứa Bí Mật', N'9786041001242', N'Phần thứ hai của series Harry Potter', CAST(125000.00 AS Decimal(18, 2)), 45, CAST(N'1998-07-02T00:00:00.0000000' AS DateTime2), N'NXB Trẻ', 360, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 5, 6),
(10, N'Harry Potter và Tên Tù Nhân Ngục Azkaban', N'9786041001243', N'Phần thứ ba của series Harry Potter', CAST(135000.00 AS Decimal(18, 2)), 40, CAST(N'1999-07-08T00:00:00.0000000' AS DateTime2), N'NXB Trẻ', 400, N'Vietnamese', CAST(N'2025-09-02T21:30:00.0000000' AS DateTime2), 5, 6)

SET IDENTITY_INSERT [dbo].[Books] OFF
GO

-- Add unique constraints and indexes
SET ANSI_PADDING ON
GO

/****** Object:  Index [UQ__Authors__A9D10534564FBDBE]    Script Date: 9/2/2025 9:30:00 PM ******/
ALTER TABLE [dbo].[Authors] ADD UNIQUE NONCLUSTERED
(
	[Email] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO

/****** Object:  Index [UQ__Books__447D36EA123456789]    Script Date: 9/2/2025 9:30:00 PM ******/
ALTER TABLE [dbo].[Books] ADD UNIQUE NONCLUSTERED
(
	[ISBN] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO

-- Add default constraints
ALTER TABLE [dbo].[Authors] ADD DEFAULT (getdate()) FOR [CreateAt]
GO

ALTER TABLE [dbo].[Books] ADD DEFAULT (getdate()) FOR [CreateAt]
GO

ALTER TABLE [dbo].[Genres] ADD DEFAULT (getdate()) FOR [CreateAt]
GO

ALTER TABLE [dbo].[Books] ADD DEFAULT ('Vietnamese') FOR [Language]
GO

-- Add foreign key constraints
ALTER TABLE [dbo].[Books] WITH CHECK ADD FOREIGN KEY([AuthorId])
REFERENCES [dbo].[Authors] ([Id])
GO

ALTER TABLE [dbo].[Books] WITH CHECK ADD FOREIGN KEY([GenreId])
REFERENCES [dbo].[Genres] ([Id])
GO
