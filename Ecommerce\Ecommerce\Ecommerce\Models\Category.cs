﻿using System.ComponentModel.DataAnnotations;

namespace Ecommerce.Models
{
    public class Category
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string? Name { get; set; }

        public string? Description { get; set; }

        public DateTime CreateAt { get; set; } = DateTime.Now;

        // Quan hệ 1-n với Product
        public ICollection<Product>? Products { get; set; }
    }
}
