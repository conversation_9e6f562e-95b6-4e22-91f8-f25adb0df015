[{"ContainingType": "Ecommerce.Controllers.CategoriesController", "Method": "GetCategories", "RelativePath": "api/Categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.CategoriesController", "Method": "CreateCategory", "RelativePath": "api/Categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "Ecommerce.Models.Category", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.CategoriesController", "Method": "GetCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.CategoriesController", "Method": "UpdateCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "category", "Type": "Ecommerce.Models.Category", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.CategoriesController", "Method": "DeleteCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.ProductsController", "Method": "GetProducts", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.ProductsController", "Method": "CreateProduct", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "product", "Type": "Ecommerce.Models.Product", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.ProductsController", "Method": "GetProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.ProductsController", "Method": "UpdateProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "product", "Type": "Ecommerce.Models.Product", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.ProductsController", "Method": "DeleteProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.UsersController", "Method": "GetUsers", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "Ecommerce.Models.Users", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "user", "Type": "Ecommerce.Models.Users", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Ecommerce.Controllers.UsersController", "Method": "DeleteUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}]