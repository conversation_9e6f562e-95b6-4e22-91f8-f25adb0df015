{"openapi": "3.0.1", "info": {"title": "Ecommerce", "version": "1.0"}, "paths": {"/api/Categories": {"get": {"tags": ["Categories"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Category"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Category"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Categories/{id}": {"get": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Category"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Category"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Users"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Users"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Users"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Users"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"Category": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}}, "additionalProperties": false}, "Product": {"required": ["name", "price"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "stock": {"type": "integer", "format": "int32"}, "categoryId": {"type": "integer", "format": "int32"}, "createAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Users": {"required": ["email", "fullName", "passwordHash", "role"], "type": "object", "properties": {"fullName": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "passwordHash": {"minLength": 1, "type": "string"}, "role": {"minLength": 1, "type": "string"}}, "additionalProperties": false}}}}