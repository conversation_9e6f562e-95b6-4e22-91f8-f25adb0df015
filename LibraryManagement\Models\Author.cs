using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace LibraryManagement.Models
{
    public class Author
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(255)]
        public string? FullName { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string? Email { get; set; }

        [StringLength(500)]
        public string? Biography { get; set; }

        [StringLength(100)]
        public string? Nationality { get; set; }

        public DateTime BirthDate { get; set; }

        public DateTime CreateAt { get; set; } = DateTime.Now;

        // Navigation property - một tác giả có thể viết nhiều sách
        [JsonIgnore]
        public ICollection<Book>? Books { get; set; }
    }
}
