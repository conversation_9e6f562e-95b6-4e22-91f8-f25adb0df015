using Microsoft.AspNetCore.Mvc;
using LibraryManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthorsController : ControllerBase
    {
        private readonly LibraryDbContext _context;

        public AuthorsController(LibraryDbContext context)
        {
            _context = context;
        }

        // GET: api/authors
        [HttpGet]
        public async Task<IActionResult> GetAuthors()
        {
            var authors = await _context.Authors
                .Include(a => a.Books)
                .ToListAsync();
            return Ok(authors);
        }

        // GET: api/authors/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetAuthor(int id)
        {
            var author = await _context.Authors
                .Include(a => a.Books)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (author == null) return NotFound();
            return Ok(author);
        }

        // POST: api/authors
        [HttpPost]
        public async Task<IActionResult> CreateAuthor([FromBody] Author author)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            if (author == null) return BadRequest("Author is required.");

            // Kiểm tra email đã tồn tại
            var existingAuthor = await _context.Authors
                .FirstOrDefaultAsync(a => a.Email == author.Email);
            if (existingAuthor != null)
                return BadRequest(new { message = "Email already exists." });

            author.Id = 0; // đảm bảo EF tự tăng Id
            author.CreateAt = DateTime.Now;

            _context.Authors.Add(author);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetAuthor), new { id = author.Id }, author);
        }

        // PUT: api/authors/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAuthor(int id, [FromBody] Author author)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            var existing = await _context.Authors.FindAsync(id);
            if (existing == null) return NotFound();

            // Kiểm tra email đã tồn tại (trừ author hiện tại)
            var emailExists = await _context.Authors
                .AnyAsync(a => a.Email == author.Email && a.Id != id);
            if (emailExists)
                return BadRequest(new { message = "Email already exists." });

            existing.FullName = author.FullName;
            existing.Email = author.Email;
            existing.Biography = author.Biography;
            existing.Nationality = author.Nationality;
            existing.BirthDate = author.BirthDate;

            await _context.SaveChangesAsync();
            return Ok(existing);
        }

        // DELETE: api/authors/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAuthor(int id)
        {
            var author = await _context.Authors.FindAsync(id);
            if (author == null) return NotFound();

            // Kiểm tra xem tác giả có sách nào không
            var hasBooks = await _context.Books.AnyAsync(b => b.AuthorId == id);
            if (hasBooks)
                return BadRequest(new { message = "Cannot delete author who has books." });

            _context.Authors.Remove(author);
            await _context.SaveChangesAsync();
            return NoContent();
        }
    }
}
