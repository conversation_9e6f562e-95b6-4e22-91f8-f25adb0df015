using Microsoft.AspNetCore.Mvc;
using LibraryManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class GenresController : ControllerBase
    {
        private readonly LibraryDbContext _context;

        public GenresController(LibraryDbContext context)
        {
            _context = context;
        }

        // GET: api/genres
        [HttpGet]
        public async Task<IActionResult> GetGenres()
        {
            var genres = await _context.Genres
                .Include(g => g.Books)
                .ToListAsync();
            return Ok(genres);
        }

        // GET: api/genres/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetGenre(int id)
        {
            var genre = await _context.Genres
                .Include(g => g.Books)
                .FirstOrDefaultAsync(g => g.Id == id);

            if (genre == null) return NotFound();
            return Ok(genre);
        }

        // POST: api/genres
        [HttpPost]
        public async Task<IActionResult> CreateGenre([FromBody] Genre genre)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            if (genre == null) return BadRequest("Genre is required.");

            // Kiểm tra tên thể loại đã tồn tại
            var existingGenre = await _context.Genres
                .FirstOrDefaultAsync(g => g.Name == genre.Name);
            if (existingGenre != null)
                return BadRequest(new { message = "Genre name already exists." });

            genre.Id = 0; // đảm bảo EF tự tăng Id
            genre.CreateAt = DateTime.Now;

            _context.Genres.Add(genre);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetGenre), new { id = genre.Id }, genre);
        }

        // PUT: api/genres/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateGenre(int id, [FromBody] Genre genre)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            var existing = await _context.Genres.FindAsync(id);
            if (existing == null) return NotFound();

            // Kiểm tra tên thể loại đã tồn tại (trừ genre hiện tại)
            var nameExists = await _context.Genres
                .AnyAsync(g => g.Name == genre.Name && g.Id != id);
            if (nameExists)
                return BadRequest(new { message = "Genre name already exists." });

            existing.Name = genre.Name;
            existing.Description = genre.Description;

            await _context.SaveChangesAsync();
            return Ok(existing);
        }

        // DELETE: api/genres/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteGenre(int id)
        {
            var genre = await _context.Genres.FindAsync(id);
            if (genre == null) return NotFound();

            // Kiểm tra xem thể loại có sách nào không
            var hasBooks = await _context.Books.AnyAsync(b => b.GenreId == id);
            if (hasBooks)
                return BadRequest(new { message = "Cannot delete genre that has books." });

            _context.Genres.Remove(genre);
            await _context.SaveChangesAsync();
            return NoContent();
        }
    }
}
