using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace LibraryManagement.Models
{
    public class Book
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(255)]
        public string? Title { get; set; }

        [StringLength(13)]
        public string? ISBN { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        public int Stock { get; set; }

        public DateTime PublishedDate { get; set; }

        [StringLength(100)]
        public string? Publisher { get; set; }

        public int Pages { get; set; }

        [StringLength(50)]
        public string? Language { get; set; } = "Vietnamese";

        public DateTime CreateAt { get; set; } = DateTime.Now;

        // Foreign Keys
        [ForeignKey("Author")]
        public int AuthorId { get; set; }
        [JsonIgnore]
        public Author? Author { get; set; }

        [Foreign<PERSON><PERSON>("Genre")]
        public int GenreId { get; set; }
        [JsonIgnore]
        public Genre? Genre { get; set; }
    }
}
