using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace LibraryManagement.Models
{
    public class Genre
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string? Name { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime CreateAt { get; set; } = DateTime.Now;

        // Navigation property - một thể loại có thể có nhiều sách
        [JsonIgnore]
        public ICollection<Book>? Books { get; set; }
    }
}
