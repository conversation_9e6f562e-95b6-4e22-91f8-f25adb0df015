using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Models
{
    public class LibraryDbContext : DbContext
    {
        public LibraryDbContext(DbContextOptions<LibraryDbContext> options) : base(options)
        { }

        // <PERSON>hai báo các bảng
        public DbSet<Author> Authors { get; set; }
        public DbSet<Book> Books { get; set; }
        public DbSet<Genre> Genres { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Cấu hình quan hệ Author - Book (1-n)
            modelBuilder.Entity<Book>()
                .HasOne(b => b.Author)
                .WithMany(a => a.Books)
                .HasForeignKey(b => b.AuthorId)
                .OnDelete(DeleteBehavior.Restrict);

            // Cấu hình quan hệ Genre - Book (1-n)
            modelBuilder.Entity<Book>()
                .HasOne(b => b.Genre)
                .WithMany(g => g.Books)
                .HasForeignKey(b => b.GenreId)
                .OnDelete(DeleteBehavior.Restrict);

            // Cấu hình unique constraint cho Author email
            modelBuilder.Entity<Author>()
                .HasIndex(a => a.Email)
                .IsUnique();

            // Cấu hình unique constraint cho Book ISBN
            modelBuilder.Entity<Book>()
                .HasIndex(b => b.ISBN)
                .IsUnique();

            // Cấu hình default values
            modelBuilder.Entity<Author>()
                .Property(a => a.CreateAt)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Book>()
                .Property(b => b.CreateAt)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Genre>()
                .Property(g => g.CreateAt)
                .HasDefaultValueSql("GETDATE()");
        }
    }
}
