using Microsoft.AspNetCore.Mvc;
using LibraryManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class BooksController : ControllerBase
    {
        private readonly LibraryDbContext _context;

        public BooksController(LibraryDbContext context)
        {
            _context = context;
        }

        // GET: api/books
        [HttpGet]
        public async Task<IActionResult> GetBooks()
        {
            var books = await _context.Books
                .Include(b => b.Author)
                .Include(b => b.Genre)
                .ToListAsync();
            return Ok(books);
        }

        // GET: api/books/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetBook(int id)
        {
            var book = await _context.Books
                .Include(b => b.Author)
                .Include(b => b.Genre)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (book == null) return NotFound();
            return Ok(book);
        }

        // GET: api/books/author/{authorId}
        [HttpGet("author/{authorId}")]
        public async Task<IActionResult> GetBooksByAuthor(int authorId)
        {
            var books = await _context.Books
                .Include(b => b.Author)
                .Include(b => b.Genre)
                .Where(b => b.AuthorId == authorId)
                .ToListAsync();
            return Ok(books);
        }

        // GET: api/books/genre/{genreId}
        [HttpGet("genre/{genreId}")]
        public async Task<IActionResult> GetBooksByGenre(int genreId)
        {
            var books = await _context.Books
                .Include(b => b.Author)
                .Include(b => b.Genre)
                .Where(b => b.GenreId == genreId)
                .ToListAsync();
            return Ok(books);
        }

        // POST: api/books
        [HttpPost]
        public async Task<IActionResult> CreateBook([FromBody] Book book)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            if (book == null) return BadRequest("Book is required.");

            // Kiểm tra Author tồn tại
            var author = await _context.Authors.FindAsync(book.AuthorId);
            if (author == null)
                return BadRequest(new { message = "Author not found." });

            // Kiểm tra Genre tồn tại
            var genre = await _context.Genres.FindAsync(book.GenreId);
            if (genre == null)
                return BadRequest(new { message = "Genre not found." });

            // Kiểm tra ISBN đã tồn tại (nếu có)
            if (!string.IsNullOrEmpty(book.ISBN))
            {
                var existingBook = await _context.Books
                    .FirstOrDefaultAsync(b => b.ISBN == book.ISBN);
                if (existingBook != null)
                    return BadRequest(new { message = "ISBN already exists." });
            }

            book.Id = 0; // đảm bảo EF tự tăng Id
            book.CreateAt = DateTime.Now;

            _context.Books.Add(book);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetBook), new { id = book.Id }, book);
        }

        // PUT: api/books/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBook(int id, [FromBody] Book book)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            var existing = await _context.Books.FindAsync(id);
            if (existing == null) return NotFound();

            // Kiểm tra Author tồn tại
            var author = await _context.Authors.FindAsync(book.AuthorId);
            if (author == null)
                return BadRequest(new { message = "Author not found." });

            // Kiểm tra Genre tồn tại
            var genre = await _context.Genres.FindAsync(book.GenreId);
            if (genre == null)
                return BadRequest(new { message = "Genre not found." });

            // Kiểm tra ISBN đã tồn tại (trừ book hiện tại)
            if (!string.IsNullOrEmpty(book.ISBN))
            {
                var isbnExists = await _context.Books
                    .AnyAsync(b => b.ISBN == book.ISBN && b.Id != id);
                if (isbnExists)
                    return BadRequest(new { message = "ISBN already exists." });
            }

            existing.Title = book.Title;
            existing.ISBN = book.ISBN;
            existing.Description = book.Description;
            existing.Price = book.Price;
            existing.Stock = book.Stock;
            existing.PublishedDate = book.PublishedDate;
            existing.Publisher = book.Publisher;
            existing.Pages = book.Pages;
            existing.Language = book.Language;
            existing.AuthorId = book.AuthorId;
            existing.GenreId = book.GenreId;

            await _context.SaveChangesAsync();
            return Ok(existing);
        }

        // DELETE: api/books/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBook(int id)
        {
            var book = await _context.Books.FindAsync(id);
            if (book == null) return NotFound();

            _context.Books.Remove(book);
            await _context.SaveChangesAsync();
            return NoContent();
        }
    }
}
