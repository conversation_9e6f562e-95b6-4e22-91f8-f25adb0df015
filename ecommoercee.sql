USE [ecommerce]
GO
/****** Object:  Table [dbo].[__EFMigrationsHistory]    Script Date: 9/2/2025 8:22:53 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[__EFMigrationsHistory](
	[MigrationId] [nvarchar](150) NOT NULL,
	[ProductVersion] [nvarchar](32) NOT NULL,
 CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY CLUSTERED 
(
	[MigrationId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Categories]    Script Date: 9/2/2025 8:22:53 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Categories](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Description] [nvarchar](255) NULL,
	[CreateAt] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Products]    Script Date: 9/2/2025 8:22:53 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Products](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](255) NOT NULL,
	[Description] [nvarchar](max) NULL,
	[Price] [decimal](18, 2) NOT NULL,
	[Stock] [int] NOT NULL,
	[Category] [nvarchar](100) NULL,
	[CreateAt] [datetime2](7) NOT NULL,
	[CategoryId] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Users]    Script Date: 9/2/2025 8:22:53 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Users](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[FullName] [nvarchar](255) NOT NULL,
	[Email] [nvarchar](255) NOT NULL,
	[PasswordHash] [nvarchar](max) NOT NULL,
	[Role] [nvarchar](50) NOT NULL,
	[CreateAt] [datetime2](7) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
INSERT [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250831132132_Init', N'9.0.8')
INSERT [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250831134507_InitialCreate', N'9.0.8')
INSERT [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250831135622_FixCreateAtColumn', N'9.0.8')
GO
SET IDENTITY_INSERT [dbo].[Categories] ON 

INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (2, N'Clothing', N'Men and Women fashion', CAST(N'2025-09-02T19:49:05.783' AS DateTime))
INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (3, N'Books', N'Novels, Comics, Educational', CAST(N'2025-09-02T19:49:05.783' AS DateTime))
INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (4, N'Home Appliances', N'Kitchen and home equipment', CAST(N'2025-09-02T19:49:05.783' AS DateTime))
INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (5, N'Toys', N'Kids toys and games', CAST(N'2025-09-02T19:49:05.783' AS DateTime))
INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (7, N'Electronics', N'Electronic devices and gadgets', CAST(N'2025-09-02T20:06:47.143' AS DateTime))
INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (8, N'Clothing', N'Men and Women fashion items', CAST(N'2025-09-02T20:07:16.960' AS DateTime))
INSERT [dbo].[Categories] ([Id], [Name], [Description], [CreateAt]) VALUES (9, N'Clothing', N'Men and Women fashion items', CAST(N'2025-09-02T20:10:08.277' AS DateTime))
SET IDENTITY_INSERT [dbo].[Categories] OFF
GO
SET IDENTITY_INSERT [dbo].[Products] ON 

INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (1, N'Laptop Dell Inspiron 15', N'Laptop 15 inch, CPU Intel i5 thế hệ 12, RAM 16GB, SSD 512GB', CAST(18990000.00 AS Decimal(18, 2)), 20, N'Laptop', CAST(N'2025-09-02T19:41:04.7761191' AS DateTime2), NULL)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (2, N'iPhone 15', N'Latest Apple smartphone', CAST(1200.00 AS Decimal(18, 2)), 10, NULL, CAST(N'2025-09-02T20:06:47.1151930' AS DateTime2), 7)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (3, N'Samsung Galaxy S24', N'Newest Samsung flagship phone', CAST(999.00 AS Decimal(18, 2)), 15, NULL, CAST(N'2025-09-02T20:06:47.1213775' AS DateTime2), 7)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (4, N'T-shirt', N'100% cotton unisex T-shirt', CAST(20.00 AS Decimal(18, 2)), 100, NULL, CAST(N'2025-09-02T20:07:16.9607237' AS DateTime2), 8)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (5, N'Jeans', N'Slim fit blue jeans', CAST(45.00 AS Decimal(18, 2)), 50, NULL, CAST(N'2025-09-02T20:07:16.9607587' AS DateTime2), 8)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (6, N'T-shirt', N'100% cotton unisex T-shirt', CAST(20.00 AS Decimal(18, 2)), 100, NULL, CAST(N'2025-09-02T20:10:08.2567121' AS DateTime2), 9)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (7, N'Jeans', N'Slim fit blue jeans', CAST(45.00 AS Decimal(18, 2)), 50, NULL, CAST(N'2025-09-02T20:10:08.2622859' AS DateTime2), 9)
INSERT [dbo].[Products] ([Id], [Name], [Description], [Price], [Stock], [Category], [CreateAt], [CategoryId]) VALUES (8, N'iPhone 8 Pro', N'Latest Apple smartphone', CAST(1200.00 AS Decimal(18, 2)), 20, NULL, CAST(N'2025-09-02T20:15:23.4747313' AS DateTime2), 3)
SET IDENTITY_INSERT [dbo].[Products] OFF
GO
SET IDENTITY_INSERT [dbo].[Users] ON 

INSERT [dbo].[Users] ([Id], [FullName], [Email], [PasswordHash], [Role], [CreateAt]) VALUES (1, N'string', N'<EMAIL>', N'string', N'string', CAST(N'2025-08-31T21:07:58.6336877' AS DateTime2))
INSERT [dbo].[Users] ([Id], [FullName], [Email], [PasswordHash], [Role], [CreateAt]) VALUES (2, N'Nguyen Van A', N'<EMAIL>', N'123456', N'Admin', CAST(N'2025-08-31T21:09:13.0863558' AS DateTime2))
SET IDENTITY_INSERT [dbo].[Users] OFF
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ__Users__A9D10534564FBDBE]    Script Date: 9/2/2025 8:22:53 PM ******/
ALTER TABLE [dbo].[Users] ADD UNIQUE NONCLUSTERED 
(
	[Email] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Categories] ADD  DEFAULT (getdate()) FOR [CreateAt]
GO
ALTER TABLE [dbo].[Products] ADD  DEFAULT (getdate()) FOR [CreateAt]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT (getdate()) FOR [CreateAt]
GO
ALTER TABLE [dbo].[Products]  WITH CHECK ADD FOREIGN KEY([CategoryId])
REFERENCES [dbo].[Categories] ([Id])
GO
